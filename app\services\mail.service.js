import { OAuth2Client } from "google-auth-library";
import nodemailer from "nodemailer";
import pool from "../config/db.js";
import { generateTempToken, runQuery, passwordHashing } from "#utils";
import dotenv from "dotenv";
import generator from "generate-password";
import { tempPasswordMail } from "../mails/tempPasswordMail.js";
import { enhancedTempPasswordMail } from "../mails/enhancedTempPasswordMail.js";
import { enhancedCaseMail } from "../mails/enhancedCaseMail.js";
import { enhancedMailService } from "../mails/enhancedMailService.js";
dotenv.config();

export const sendOTP = async (email) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let otp = generator.generate({
      length: 5,
      numbers: true,
      lowercase: false,
      uppercase: false,
    });
    let otp_sql = `INSERT INTO temp_otp (email, code, exp_time) VALUES (?, ? , ADDTIME(NOW(),'0:05'))`;
    let otp_result = await runQuery(con, otp_sql, [email, otp]);
    await con.commit();
    const myOAuth2Client = new OAuth2Client(
      process.env.GOOGLE_MAILER_CLIENT_ID,
      process.env.GOOGLE_MAILER_CLIENT_SECRET
    );

    myOAuth2Client.setCredentials({
      refresh_token: process.env.GOOGLE_MAILER_REFRESH_TOKEN,
    });

    const myAccessTokenObject = await myOAuth2Client.getAccessToken();
    const myAccessToken = myAccessTokenObject?.token;

    let transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        type: "OAuth2",
        user: process.env.ADMIN_EMAIL_ADDRESS,
        clientId: process.env.GOOGLE_MAILER_CLIENT_ID,
        clientSecret: process.env.GOOGLE_MAILER_CLIENT_SECRET,
        refreshToken: process.env.GOOGLE_MAILER_REFRESH_TOKEN,
        accessToken: myAccessToken,
      },
    });

    let info = await transporter.sendMail({
      to: email, // list of receivers
      subject: "OTP for Verification", // Subject line
      text: `Your OTP is: ${otp}`, // plain text body
    });
  } catch (err) {
    await con.rollback();
    return null;
  } finally {
    con.destroy();
  }
};

export const sendTempPassword = async (email, password, first_name) => {
  try {
    await tempPasswordMail(email, password, first_name)
    return 1;
  } catch (err) {
    console.log(err);
    return 0;
  }
};

/**
 * Enhanced temp password mail with Graph API support
 * @param {string} email - Recipient email
 * @param {string} password - Temporary password
 * @param {string} first_name - Recipient first name
 * @param {number} lf_id - Law firm ID (optional)
 * @param {string} user_id - User ID for Graph API (optional)
 * @returns {Object} Send result
 */
export const sendEnhancedTempPassword = async (email, password, first_name, lf_id = null, user_email = null) => {
  try {
    const result = await enhancedTempPasswordMail(email, password, first_name, lf_id, user_email);
    return { success: true, result };
  } catch (err) {
    console.log('Enhanced temp password error:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Enhanced case mail with Graph API support
 * @param {Object} mailData - Mail data object
 * @returns {Object} Send result
 */
export const sendEnhancedCaseMail = async (mailData) => {
  try {
    const {
      email, link, first_name, path, bgColor, textColor,
      title, header, body, footer, logo, user, lf_id, user_email
    } = mailData;

    const result = await enhancedCaseMail(
      email, link, first_name, path, bgColor, textColor,
      title, header, body, footer, logo, user, lf_id, user_email
    );
    return { success: true, result };
  } catch (err) {
    console.log('Enhanced case mail error:', err);
    return { success: false, error: err.message };
  }
};

// ============= NEW ENHANCED MAIL FUNCTIONS =============

/**
 * Enhanced Active Questionnaire Mail
 */
export const sendEnhancedActiveQuestionnaireMail = async (email, first_name, qtn_id, lf_id = null, user_email = null) => {
  try {
    const result = await enhancedMailService.sendActiveQuestionnaireMail(email, first_name, qtn_id, lf_id, user_email);
    return { success: true, result };
  } catch (err) {
    console.log('Enhanced active questionnaire mail error:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Enhanced Update Questionnaire Mail
 */
export const sendEnhancedUpdateQuestionnaireMail = async (email, first_name, qtn_name, qtn_id, lf_id = null, user_email = null) => {
  try {
    const result = await enhancedMailService.sendUpdateQuestionnaireMail(email, first_name, qtn_name, qtn_id, lf_id, user_email);
    return { success: true, result };
  } catch (err) {
    console.log('Enhanced update questionnaire mail error:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Enhanced Submit Case Mail
 */
export const sendEnhancedSubmitCaseMail = async (email, case_id, first_name, lf_id = null, user_email = null) => {
  try {
    const result = await enhancedMailService.sendSubmitCaseMail(email, case_id, first_name, lf_id, user_email);
    return { success: true, result };
  } catch (err) {
    console.log('Enhanced submit case mail error:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Enhanced Submit Case Client Mail
 */
export const sendEnhancedSubmitCaseClientMail = async (email, first_name, customOptions = {}, lf_id = null, user_email = null) => {
  try {
    const result = await enhancedMailService.sendSubmitCaseClientMail(email, first_name, customOptions, lf_id, user_email);
    return { success: true, result };
  } catch (err) {
    console.log('Enhanced submit case client mail error:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Enhanced Call Me Mail
 */
export const sendEnhancedCallMeMail = async (email, first_name, phone_number, best_time, lf_id = null, user_email = null) => {
  try {
    const result = await enhancedMailService.sendCallMeMail(email, first_name, phone_number, best_time, lf_id, user_email);
    return { success: true, result };
  } catch (err) {
    console.log('Enhanced call me mail error:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Enhanced Case Mail (full customization)
 */
export const sendEnhancedCaseMailFull = async (email, link, first_name, customOptions = {}, lf_id = null, user_email = null) => {
  try {
    const result = await enhancedMailService.sendCaseMail(email, link, first_name, customOptions, lf_id, user_email);
    return { success: true, result };
  } catch (err) {
    console.log('Enhanced case mail full error:', err);
    return { success: false, error: err.message };
  }
};

/**
 * Enhanced Chaser Mail
 */
export const sendEnhancedChaserMail = async (email, link, first_name, customOptions = {}, lf_id = null, user_email = null) => {
  try {
    const result = await enhancedMailService.sendChaserMail(email, link, first_name, customOptions, lf_id, user_email);
    return { success: true, result };
  } catch (err) {
    console.log('Enhanced chaser mail error:', err);
    return { success: false, error: err.message };
  }
};

// ============= SMART WRAPPER FUNCTIONS =============
// These functions automatically detect law firm ID and user email from request context

/**
 * Smart wrapper for temp password mail - automatically uses Graph API if available
 * @param {string} email - Recipient email
 * @param {string} password - Temporary password
 * @param {string} first_name - Recipient first name
 * @param {Object} userContext - User context from req.user (optional)
 * @returns {Object} Send result
 */
export const sendTempPasswordSmart = async (email, password, first_name, userContext = null) => {
  const lf_id = userContext?.lf_id || null;
  const user_email = userContext?.email || null;

  try {
    // Try enhanced version first (with Graph API support)
    const result = await sendEnhancedTempPassword(email, password, first_name, lf_id, user_email);
    if (result.success) {
      return result;
    }
  } catch (error) {
    console.log('Enhanced temp password failed, falling back to original:', error);
  }

  // Fallback to original SendGrid-only version
  const fallbackResult = await sendTempPassword(email, password, first_name);
  return { success: fallbackResult === 1, legacy: true };
};

/**
 * Smart wrapper for case mail - automatically uses Graph API if available
 * @param {Object} mailData - Mail data with all case mail parameters
 * @param {Object} userContext - User context from req.user (optional)
 * @returns {Object} Send result
 */
export const sendCaseMailSmart = async (mailData, userContext = null) => {
  const lf_id = userContext?.lf_id || mailData.lf_id || null;
  const user_email = userContext?.email || null;

  try {
    // Try enhanced version first (with Graph API support)
    const result = await sendEnhancedCaseMailFull(
      mailData.email,
      mailData.link,
      mailData.first_name,
      mailData,
      lf_id,
      user_email
    );
    if (result.success) {
      return result;
    }
  } catch (error) {
    console.log('Enhanced case mail failed, falling back to original:', error);
  }

  // Fallback to original SendGrid-only version
  try {
    const { caseMail } = await import('../mails/caseMail.js');
    await caseMail(
      mailData.email,
      mailData.link,
      mailData.first_name,
      mailData.path,
      mailData.bgColor,
      mailData.textColor,
      mailData.title,
      mailData.header,
      mailData.body,
      mailData.footer,
      mailData.logo,
      mailData.user
    );
    return { success: true, legacy: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

/**
 * Smart wrapper for chaser mail - automatically uses Graph API if available
 * @param {Object} mailData - Mail data with all chaser mail parameters
 * @param {Object} userContext - User context from req.user (optional)
 * @returns {Object} Send result
 */
export const sendChaserMailSmart = async (mailData, userContext = null) => {
  const lf_id = userContext?.lf_id || mailData.lf_id || null;
  const user_email = userContext?.email || null;

  try {
    // Try enhanced version first (with Graph API support)
    const result = await sendEnhancedChaserMail(
      mailData.email,
      mailData.link,
      mailData.first_name,
      mailData,
      lf_id,
      user_email
    );
    if (result.success) {
      return result;
    }
  } catch (error) {
    console.log('Enhanced chaser mail failed, falling back to original:', error);
  }

  // Fallback to original SendGrid-only version
  try {
    const { chaserMail } = await import('../mails/chaserMail.js');
    await chaserMail(
      mailData.email,
      mailData.link,
      mailData.first_name,
      mailData.path,
      mailData.bgColor,
      mailData.textColor,
      mailData.title,
      mailData.header,
      mailData.body,
      mailData.footer,
      mailData.logo,
      mailData.user
    );
    return { success: true, legacy: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
};
export const verifyOTP = async (email, otp) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let otp_sql = `SELECT code FROM temp_otp WHERE email = ? AND exp_time > NOW() ORDER BY exp_time DESC LIMIT 1;`;
    let otp_result = await runQuery(con, otp_sql, [email, otp]);
    let token = await generateTempToken(email);
    await con.commit;
    if (otp_result.length > 0) {
      return [otp_result[0].code, token];
    } else {
      return null;
    }
  } catch (err) {
    await con.rollback();
    return false;
  } finally {
    con.destroy();
  }
};

export const deleteOTP = async (email) => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let otp_sql = `DELETE FROM temp_otp WHERE email = ?`;
    let otp_result = await runQuery(con, otp_sql, [email]);
    await con.commit();
  } catch (err) {
    con.rollback();
    return false;
  } finally {
    con.destroy();
  }
};

export const getMailType = async () => {
  let con = await pool.getConnection();
  try {
    await con.beginTransaction();
    let mailType = await runQuery(con, `SELECT * FROM mail_type`);
    await con.commit();
    return mailType;
  } catch (err) {
    con.rollback();
    return false;
  } finally {
    con.destroy();
  }
}
